Open Canvas – quick reference for agents

• Build/lint/format: from repo root run `yarn build` (Turbo across workspaces), `yarn lint` / `yarn lint:fix`, `yarn format` / `yarn format:check` (see [package.json](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/package.json) and [turbo.json](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/turbo.json)).
• Dev servers: agents `cd apps/agents && yarn dev` (LangGraph CLI on :54367; config in [langgraph.json](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/langgraph.json)); web `cd apps/web && yarn dev` (Next.js 14).
• Tests: Vitest is used (devDependency). Web evals: `cd apps/web && yarn eval` (uses [ls.vitest.config.ts](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/web/ls.vitest.config.ts)); run a single test: `vitest run path/to/file.eval.ts -t "name" --config ls.vitest.config.ts`.
• Monorepo layout (Yarn workspaces): apps/web (Next frontend), apps/agents (LangGraph server), packages/shared (types/models/utils), packages/evals (eval utilities). See [apps/agents/src/...](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/agents/src) and [apps/web/src/...](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/web/src).
• Agents architecture: primary graphs under [open-canvas](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/agents/src/open-canvas/index.ts), [reflection](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/agents/src/reflection/index.ts), [web-search](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/agents/src/web-search/index.ts), [summarizer](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/agents/src/summarizer/index.ts).
• Frontend architecture: Next app/router with API routes under `app/api/*`; LangGraph base URL is `LANGGRAPH_API_URL` (default http://localhost:54367) in [constants.ts](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/web/src/constants.ts); web worker for graph streaming under [workers/graph-stream](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/web/src/workers/graph-stream/stream.worker.ts).
• Data/auth: Supabase auth (envs in apps/web .env); agents optionally read `SUPABASE_SERVICE_ROLE` to fetch user (see [utils.ts](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/agents/src/utils.ts)); threads/memories stored via LangGraph store; cookies like `oc_assistant_id_v2` used on web.
• Model config: central list in [packages/shared/src/models.ts](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/packages/shared/src/models.ts); agent-side model selection in [apps/agents/src/utils.ts#getModelConfig](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/agents/src/utils.ts#L162-L301).
• TypeScript: strict across repo; NodeNext in libs ([apps/agents/tsconfig.json](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/agents/tsconfig.json)); Next TS config in [apps/web/tsconfig.json](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/web/tsconfig.json) with `@/*` path alias.
• Linting: ESLint extends recommended + @typescript-eslint; notable rules: error on `no-floating-promises`/`no-misused-promises`, unused vars warning or error, many stylistic rules relaxed; web uses `unused-imports` plugin (see [apps/web/.eslintrc.json](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/web/.eslintrc.json)).
• Formatting: Prettier repo-wide (semi: true, double quotes, printWidth 80, trailingComma: es5) – see [.prettierrc files](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/web/.prettierrc).
• Imports: prefer named exports (rule `import/prefer-default-export` off), ignore package extensions; use `@/*` in web and relative paths elsewhere.
• Error handling: throw descriptive errors for missing config (e.g., model name, store); validate with Zod in agents’ node schemas (see `schemas.ts` under nodes); avoid floating promises per lint rules.
• Environment: root `.env` for agents; `apps/web/.env` for frontend; configure OpenAI/Anthropic/Google/Fireworks/Groq/Firecrawl per [README.md](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/README.md).
• Internal APIs: LangGraph served via CLI (HTTP + SSE); Next API routes wrap storage, sharing, scraping, feedback (see [apps/web/src/app/api](file:///Users/<USER>/Documents/Projetos%202025/open-canvas/apps/web/src/app/api)).
• Package scripts: agents `build` compiles TS to dist; shared/evals similar; web uses Next `build/start`; root scripts fan out via Turbo.
• Testing notes: evals use Vitest with LangSmith reporter; `.env` is loaded via dotenv in test config.
• Tooling rules: No Cursor/Claude/Windsurf/Cline/Goose/Copilot instruction files present at repo root.
• Contribute: follow lint/format before commits; prefer small PRs; keep model lists/types in shared; keep agent graph nodes pure and schema-validated.
