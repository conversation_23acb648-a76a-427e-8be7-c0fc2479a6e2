Project discovery: open-canvas

This document captures a short discovery of the repository, its architecture, core features, AI integrations, and concrete recommendations for how parts of it can be reused or adapted for an AI-assisted translation suite.

## Task receipt & plan

I inspected the repository to map its main components, technologies, AI integrations, and code patterns. I looked through the monorepo workspace (root `package.json`), the frontend (`apps/web`), the agents (`apps/agents`), and the shared types (`packages/shared`). Below are findings, relevant code references, and suggested next steps.

## Checklist (your requested items)

- [x] 1. Examine the project structure and identify the main components
- [x] 2. Understand technologies and frameworks used
- [x] 3. Analyze core functionality and features
- [x] 4. Identify AI/ML integrations and capabilities
- [x] 5. Assess applicability/integration with an AI-assisted translation suite
- [x] 6. Highlight relevant code patterns, APIs, and architectural decisions

## Files and areas inspected (representative)

- `package.json` (root) — workspace and scripts
- `apps/web/package.json`, `apps/web/src` — Next.js 14 frontend, UI components, editors
- `apps/agents/package.json`, `apps/agents/src` — LangGraph agents, graph definitions, nodes
- `packages/shared/src` — shared types and small utilities used by frontend & agents
- Agent graphs & nodes: `apps/agents/src/reflection/*`, `apps/agents/src/open-canvas/*`, `apps/agents/src/summarizer/*`, `apps/agents/src/thread-title/*`
- Prompts and state descriptors: `apps/agents/src/**/prompts.ts`, `apps/agents/src/**/state.ts`

## High-level architecture

- Monorepo (Yarn workspaces) with two primary app-level workspaces:
  - `apps/web` — React + Next.js frontend (UI, editors, auth, rendering, calls to LangGraph backend)
  - `apps/agents` — LangGraph-based agent graphs and tool bindings that run as a LangGraph server
- `packages/shared` — shared types, constants, model configs that are consumed by both frontend and agents
- Agents are authored as LangGraph StateGraphs (annotations + nodes) and compiled/served by LangGraph CLI
- Frontend uses `@langchain/langgraph-sdk` to interact with threads and the LangGraph server, and `@opencanvas/shared` types for contracts

Diagram (conceptual):

Frontend (Next.js, React, editors)
  ↕ (HTTP / LangGraph SDK)
LangGraph server (agents) — StateGraph runnables, nodes, tool-calls, memory store
  ↕ (store)
Memory / persistence (LangGraph store, Supabase for auth on frontend)

## Technologies & frameworks used

- TypeScript (monorepo), Yarn workspaces, Turborepo for builds
- Frontend: Next.js 14, React 18, TailwindCSS, Blocknote editor, CodeMirror, UI libs (Radix, Assistant UI components)
- Agents & orchestration: @langchain/langgraph, @langchain/langgraph-sdk, LangChain model wrappers (@langchain/openai, @langchain/anthropic, @langchain/ollama, etc.)
- Model / LLM integrations: OpenAI, Anthropic, Fireworks, Google GenAI, Ollama support (local models)
- Persistence/infra: Supabase (auth and optional data), LangSmith for tracing, a LangGraph store (used by graphs for memory)
- Utilities: FFmpeg (media), PDF parsing, Groq/exa/firecrawl SDKs for web and search tools

## Core functionality and features

- Collaborative writing canvas that can hold artifacts (text, code, markdown).
- Integrated conversational assistant(s) that operate on artifacts and chat history.
- Memory and reflection system: the agents generate "reflections" about the user and conversation, store them in a memory namespace, and include them in subsequent prompts.
- Quick actions: user-defined or pre-built prompt templates / quick actions that apply to the current artifact.
- Artifact versioning: artifacts have versions so users can view previous versions.
- Live markdown rendering + combined code/markdown editing.
- Built-in agent graphs for tasks like summarization, thread-title generation, reflection generation, artifact rewriting and followups.
- Web search and scraping tooling integrations for enrichment.

Key implementation notes:
- Agents are assembled as LangGraph StateGraphs. Each graph defines typed state annotations (`Annotation.Root`) in `state.ts`, prompts in `prompts.ts`, and runnable nodes in `nodes/*.ts`.
- The reflection and followup workflows read/write a memory namespace (`const memoryNamespace = ["memories", assistantId]`) and use `store.get`/`store.put` to persist user reflections.

## AI / ML integrations and capabilities

- Uses LangChain and LangGraph primitives to orchestrate LLM calls and tool usage.
- Model configuration abstraction: utilities `getModelConfig` and `getModelFromConfig` are used widely to select and instantiate a model from the LangGraph runnable `config` (see `apps/agents/src/*` nodes).
- Supported providers: OpenAI, Anthropic, Ollama (local), Fireworks, Google GenAI, Groq, plus ability to add others by updating `packages/shared/src/models.ts` and agent utils.
- Tool calling and graph-binding: agents bind tools (e.g., `generate_reflections`) so models can invoke structured tool calls in a graph execution.
- Memory & reflection pipeline: graphs generate reflections using small models, format them, and store as structured memories (used as style rules / user preferences).
- LangGraph and LangChain message types (HumanMessage, AIMessage, AIMessageChunk, BaseMessage) used throughout; graphs expect typed I/O via annotations.

Examples of AI-specific code patterns (references):
- `StateGraph(ReflectionGraphAnnotation)` builder + `.compile().withConfig({ runName: "reflection" })` — typed graph compilation.
- `getModelFromConfig(config, { temperature: ..., maxTokens: ... })` — provider-agnostic model instantiation.
- Memory read/write: `await store.get(memoryNamespace, memoryKey)` / `await store.put(memoryNamespace, memoryKey, newMemories)`.
- Tool binding: `.bindTools([generateReflectionTool], { tool_choice: "generate_reflections" })` and parsing `result.tool_calls?.[0]`.

## Code & API surfaces particularly relevant to a translation suite

- Model configuration utilities (`getModelConfig`, `getModelFromConfig`) — make it easy to swap providers and tune parameters per task (useful for A/B testing different MT engines).
- Memory store pattern and structured reflections — can be adapted into a translation memory (TM) / glossary store keyed by project, language-pair, or user.
- Quick actions system (frontend + prompt templates in `prompts.ts`) — reuse for user-defined translation tasks (e.g., "Translate to PT-BR, preserve formal tone", or custom glossaries).
- Editor integrations (Blocknote, CodeMirror, live markdown rendering) — provide UI components for side-by-side source/target editing, inline suggestions, and diffs.
- Graph-based pipelines (LangGraph StateGraph nodes) — ideal to implement multi-step translation workflows: pre-process (normalize), translate (LLM or MT API), post-process (detokenize/format), QA (back-translate or classifier), and store versions.
- Examples of agent node patterns to adapt:
  - `generate-artifact` node — orchestrates creation of artifact using a model + reflections
  - `generateFollowup` & `summarizer` — generate short metadata (titles, summaries, QA checks) useful for translation metadata or quality scoring
  - `thread-title` — shows integration with `langgraph-sdk` for updating thread metadata via `Client`.

## How this maps to common translation suite concerns

- Translation memory & glossary: adapt memory namespace pattern to store bilingual phrase pairs, user-approved translations, and style rules. Use structured JSON reflections for glossaries.
- Customizable prompt templates: reuse quick actions to let users create and persist per-project translation instructions (formal vs. informal, domain terminology).
- Model selection & fallback: reuse `getModelFromConfig` to call an MT API (e.g., Google, OpenAI, or local Ollama models), with fallbacks to other engines.
- Quality checks: implement QA graphs that run back-translation or consistency tests (LangGraph nodes can call multiple models/tools and aggregate results).
- Editor UX: use Blocknote/CodeMirror components combined with artifact versioning to show source ↔ target diffs and history.
- Local/on-prem models: Ollama integration pattern supports running local models for privacy-sensitive translation workflows.

## Recommended next steps to integrate/adapt open-canvas for translation

1. Identify the minimum viable pieces to reuse:
   - `packages/shared` for model config types and helpers
   - `apps/agents/src` graph templates for building translation graphs
   - `apps/web/src/components/editor/*` (Blocknote/CodeMirror) for side-by-side editing
2. Design a Translation Memory graph: reuse memory namespace pattern and store bilingual entries keyed by artifact ID + language pair.
3. Create quick-action templates for common translations and tone-preserving transformations (store templates in the same quick-action system).
4. Implement a small LangGraph StateGraph that: normalize source -> call MT model -> post-process -> QA -> store translation as new artifact version.
5. Add a QA node that uses either a smaller model or heuristic rules (and optionally run back-translation) to produce a quality score and suggested fixes.
6. Prototype with local Ollama model and one cloud provider to compare results and latency.

## Relevant risks & edge cases

- Model hallucination: translation LLMs can hallucinate content or add/omit data — include QA/back-translation and human-in-the-loop approval in your pipeline.
- Terminology drift: use a translation memory (TM) and enforced glossaries in prompts to reduce inconsistency.
- Privacy/compliance: if translations handle private text, prefer on-prem models (Ollama) or encrypted storage; Supabase and cloud providers may have policies to review.
- Latency & cost: large LLMs are expensive; the repo pattern of using a smaller model for reflection/followups and configurable model selection is useful.

## Notable code patterns & files to review first (quick pointer)

- `apps/agents/src/utils.js` — contains `getModelConfig` / `getModelFromConfig` helpers used per-node
- `apps/agents/src/open-canvas/nodes/*.ts` — concrete node implementations for artifact generation, update, summarization
- `apps/agents/src/reflection/*` — memory/reflection generation and storage pattern
- `apps/agents/src/summarizer/*`, `thread-title/*` — examples of small-model jobs (metadata generation)
- `apps/web/src/components/*` — editor & UI components (Blocknote, codemirror usage)
- `packages/shared/src/models.ts` — where provider names and model metadata are stored (add translation engines here)

## Quick "try it locally" (optional)

To run the project locally (notes from README):

1. Build workspace (root):

```bash
yarn install
yarn build
```

2. Start LangGraph server (agents):

```bash
cd apps/agents
yarn dev
# LangGraph server will start on the configured port (default in README: 54367)
```

3. Start frontend:

```bash
cd apps/web
yarn dev
# open http://localhost:3000
```

## Requirements coverage

- 1. Project structure / main components — Done (see "Files and areas inspected")
- 2. Technologies & frameworks — Done (listed under "Technologies & frameworks used")
- 3. Core functionality & features — Done (section "Core functionality and features")
- 4. AI/ML integrations — Done (section "AI / ML integrations and capabilities")
- 5. Applicability to translation suite — Done (sections "How this maps..." and "Recommended next steps")
- 6. Relevant patterns/APIs/decisions — Done (many pointers and files listed)

## Closing summary

Open Canvas is a modern LangChain + LangGraph-based monorepo that cleanly separates a Next.js frontend from a LangGraph agent backend. The repo already implements memory/reflection stores, quick actions, versioned artifacts, and a graph-based orchestration pattern that maps well to multi-step translation pipelines (TM management, model selection, QA, and editor UX). I recommend starting by adapting the memory namespace and graph node patterns to implement a Translation Memory and a small translation pipeline graph; reuse the editor components for side-by-side editing and quick actions for translation templates.

Files created/edited in this step:

- `PROJECT.md` — this discovery document (summary and recommended next steps)

If you want, I can now:

- generate a focused plan and a minimal prototype: (1) a LangGraph translation graph skeleton, (2) a small TM memory schema in `packages/shared`, and (3) a simple frontend translation quick-action UI; or
- produce code patches that add a translation-memory store + one sample translation StateGraph node.

Choose which of the two you'd like me to implement next.
